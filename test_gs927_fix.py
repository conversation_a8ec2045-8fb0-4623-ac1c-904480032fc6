#!/usr/bin/env python3
"""
Test script for GhostScript 9.27 compatibility fixes
Demonstrates the fix for "Appearance dictionary (AP) lacks the mandatory normal (N) appearance" error
"""

import sys
import subprocess
import tempfile
from pathlib import Path
import logging

# Import the enhanced comb_compatibility module
from comb_compatibility import Comb<PERSON>ield<PERSON><PERSON><PERSON>

def test_gs927_compatibility():
    """Test the GhostScript 9.27 compatibility fixes"""
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    logger = logging.getLogger(__name__)
    
    logger.info("Testing GhostScript 9.27 compatibility fixes")
    logger.info("=" * 60)
    
    # Test with a sample PDF (you would replace this with your actual PDF)
    input_pdf = "your_input.pdf"  # Replace with your PDF path
    
    if not Path(input_pdf).exists():
        logger.error(f"Input PDF not found: {input_pdf}")
        logger.info("Please update the 'input_pdf' variable with your actual PDF path")
        return False
    
    # Create temporary output files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test without GS 9.27 mode (original behavior)
        output_normal = temp_path / "output_normal.pdf"
        logger.info(f"Testing normal mode: {output_normal}")
        
        try:
            with CombFieldFixer(input_pdf, gs927_mode=False) as fixer:
                if fixer.fix_comb_fields():
                    if fixer.save(output_normal):
                        logger.info("✓ Normal mode: PDF fixed successfully")
                    else:
                        logger.error("✗ Normal mode: Failed to save PDF")
                        return False
                else:
                    logger.error("✗ Normal mode: Failed to fix comb fields")
                    return False
        except Exception as e:
            logger.error(f"✗ Normal mode failed: {e}")
            return False
        
        # Test with GS 9.27 mode (enhanced compatibility)
        output_gs927 = temp_path / "output_gs927.pdf"
        logger.info(f"Testing GS 9.27 mode: {output_gs927}")
        
        try:
            with CombFieldFixer(input_pdf, gs927_mode=True) as fixer:
                if fixer.fix_comb_fields():
                    if fixer.save(output_gs927):
                        logger.info("✓ GS 9.27 mode: PDF fixed successfully")
                    else:
                        logger.error("✗ GS 9.27 mode: Failed to save PDF")
                        return False
                else:
                    logger.error("✗ GS 9.27 mode: Failed to fix comb fields")
                    return False
        except Exception as e:
            logger.error(f"✗ GS 9.27 mode failed: {e}")
            return False
        
        # Test GhostScript processing if available
        logger.info("\nTesting GhostScript processing...")
        
        # Check if GhostScript is available
        try:
            result = subprocess.run(['gs', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                gs_version = result.stdout.strip()
                logger.info(f"GhostScript version: {gs_version}")
                
                # Test flattening with both versions
                for mode, pdf_file in [("normal", output_normal), ("gs927", output_gs927)]:
                    flattened_pdf = temp_path / f"flattened_{mode}.pdf"
                    
                    logger.info(f"Flattening {mode} mode PDF...")
                    
                    gs_cmd = [
                        'gs', '-o', str(flattened_pdf),
                        '-sDEVICE=pdfwrite',
                        '-dPDFSETTINGS=/prepress',
                        str(pdf_file)
                    ]
                    
                    try:
                        result = subprocess.run(gs_cmd, capture_output=True, text=True, timeout=30)
                        
                        if result.returncode == 0:
                            logger.info(f"✓ {mode} mode: GhostScript flattening successful")
                            if flattened_pdf.exists():
                                size = flattened_pdf.stat().st_size
                                logger.info(f"  Flattened PDF size: {size:,} bytes")
                        else:
                            logger.error(f"✗ {mode} mode: GhostScript flattening failed")
                            if result.stderr:
                                # Check for the specific error we're trying to fix
                                if "lacks the mandatory normal (N) appearance" in result.stderr:
                                    logger.error(f"  Found the AP/N appearance error in {mode} mode!")
                                logger.error(f"  GhostScript error: {result.stderr[:500]}...")
                    
                    except subprocess.TimeoutExpired:
                        logger.error(f"✗ {mode} mode: GhostScript processing timed out")
                    except Exception as e:
                        logger.error(f"✗ {mode} mode: GhostScript processing error: {e}")
            
            else:
                logger.warning("GhostScript not available or not working properly")
                logger.info("You can manually test the generated PDFs with:")
                logger.info(f"  gs -o flattened.pdf -sDEVICE=pdfwrite {output_gs927}")
        
        except FileNotFoundError:
            logger.warning("GhostScript (gs) not found in PATH")
            logger.info("Install GhostScript to test flattening:")
            logger.info("  macOS: brew install ghostscript")
            logger.info("  Ubuntu: sudo apt-get install ghostscript")
            logger.info("  Windows: Download from https://www.ghostscript.com/")
        
        except Exception as e:
            logger.error(f"Error testing GhostScript: {e}")
        
        logger.info("\n" + "=" * 60)
        logger.info("Test completed!")
        logger.info(f"Generated files in: {temp_dir}")
        logger.info("Files will be automatically cleaned up when script exits.")
        
        return True

def main():
    """Main function"""
    if len(sys.argv) > 1:
        # If a PDF path is provided as argument, use it
        global input_pdf
        input_pdf = sys.argv[1]
    
    success = test_gs927_compatibility()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
