#!/usr/bin/env python3
"""
PDF Comb Field Compatibility Script for GhostScript Flattening
Fixes issues with numberOfCells fields losing content when flattened with GhostScript 9.27

Enhanced with special handling for numberOfCells attributes and GhostScript 9.27 compatibility.
Works with both traditional comb fields and custom numberOfCells fields.

Requires pypdf version 5.6.0
"""

import sys
import re
from pathlib import Path
import logging

# Import pypdf 5.6.0
try:
    import pypdf
    from pypdf import PdfWriter, PdfReader
    from pypdf.generic import (
        DictionaryObject, ArrayObject, IndirectObject, TextStringObject,
        NameObject, BooleanObject, NumberObject
    )
    from pypdf.errors import PdfReadError
except ImportError:
    print("Error: pypdf 5.6.0 is required but not installed.")
    print("Please install it with:")
    print("  pip install pypdf==5.6.0")
    sys.exit(1)

# Configure logging for better debugging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

class CombFieldFixer:
    """
    PDF Comb Field Fixer for pypdf 5.6.0

    Provides improved error handling, compression, and robustness features
    for fixing comb fields in PDF forms for better GhostScript compatibility.
    """

    def __init__(self, input_pdf, gs927_mode=False):
        """Initialize with input PDF path and optional GS 9.27 compatibility mode"""
        self.input_path = Path(input_pdf)
        self.reader = None
        self.writer = None
        self.logger = logging.getLogger(__name__)
        self.gs927_mode = gs927_mode  # Enable GhostScript 9.27 compatibility mode

        # Validate input file
        if not self.input_path.exists():
            raise FileNotFoundError("PDF file not found: {}".format(self.input_path))

        if self.gs927_mode:
            self.logger.info("GhostScript 9.27 compatibility mode enabled")

        self.open_pdf()

    def open_pdf(self):
        """Open PDF file and prepare for processing with enhanced error handling"""
        try:
            # Open PDF with pypdf 5.6.0
            self.reader = PdfReader(str(self.input_path))

            # Enhanced error handling for encrypted PDFs
            if self.reader.is_encrypted:
                self.logger.warning("PDF is encrypted. Attempting to decrypt...")
                try:
                    if not self.reader.decrypt(""):
                        raise PdfReadError("PDF requires a password for modification")
                    self.logger.info("Successfully decrypted PDF with empty password")
                except Exception as e:
                    raise PdfReadError("Failed to decrypt PDF: {}".format(e))

            # Initialize writer with compression (if supported)
            try:
                self.writer = PdfWriter(compress_identical_objects=True)
            except TypeError:
                # Fallback for older pypdf versions that don't support this parameter
                self.writer = PdfWriter()
                self.logger.debug("Using PdfWriter without compression (older pypdf version)")

            # Clone the entire document structure
            try:
                self.writer.clone_reader_document_root(self.reader)
                self.logger.info("Successfully cloned PDF document structure")
            except (AttributeError, Exception) as e:
                self.logger.error("Failed to clone document: {}".format(e))
                # Fallback to manual page copying
                self._manual_copy_fallback()

            return True

        except PdfReadError as e:
            self.logger.error("PDF read error: {}".format(e))
            return False
        except Exception as e:
            self.logger.error("Unexpected error opening PDF: {}".format(e))
            return False

    def _manual_copy_fallback(self):
        """Fallback method for manual PDF copying when clone fails"""
        self.logger.info("Using manual copy fallback method")

        # Copy all pages using pypdf 5.6.0
        for page in self.reader.pages:
            self.writer.add_page(page)

        # Copy essential document catalog entries
        try:
            root = self.reader.trailer['/Root']
            essential_keys = ['/AcroForm', '/Names', '/Dests', '/ViewerPreferences']

            for key in essential_keys:
                if key in root:
                    self.writer._root_object[NameObject(key)] = root[key]
                    self.logger.debug("Copied {} to writer".format(key))
        except Exception as e:
            self.logger.warning("Could not copy catalog entries: {}".format(e))

    def fix_comb_fields(self):
        """
        Fix comb fields for better GhostScript compatibility

        Enhanced for pypdf 5.6.0 with improved error handling and robustness.
        """
        try:
            if not self.writer or not self.writer._root_object:
                raise PdfReadError("Writer not properly initialized")

            if '/AcroForm' not in self.writer._root_object:
                self.logger.warning("No AcroForm found in PDF - no form fields to fix")
                return False

            acroform = self.writer._root_object['/AcroForm']
            if '/Fields' not in acroform:
                self.logger.warning("No fields found in AcroForm")
                return False

            self.logger.info("Starting comb field fixes...")

            # Configure AcroForm for better flattening
            self._configure_acroform(acroform)

            # Process all fields with enhanced error handling
            fields_processed = self._process_fields_recursive(acroform['/Fields'])

            if fields_processed > 0:
                self.logger.info("Successfully processed {} fields".format(fields_processed))
                return True
            else:
                self.logger.warning("No fields were processed")
                return False

        except PdfReadError as e:
            self.logger.error("PDF read error while fixing comb fields: {}".format(e))
            return False
        except Exception as e:
            self.logger.error("Unexpected error fixing comb fields: {}".format(e))
            return False
            
    def _configure_acroform(self, acroform):
        """
        Configure AcroForm for better GhostScript compatibility

        Enhanced for pypdf 5.6.0 with improved robustness and error handling.
        """
        try:
            # Set NeedAppearances to False - forces PDF to have proper appearances
            # This is crucial for GhostScript compatibility
            acroform[NameObject('/NeedAppearances')] = BooleanObject(False)
            self.logger.info("Set NeedAppearances to False for better flattening")

            # Remove XFA if present (can interfere with flattening)
            if '/XFA' in acroform:
                self.logger.info("Removing XFA for better GhostScript compatibility")
                del acroform['/XFA']

            # Ensure proper form flags are set
            if '/Flags' not in acroform:
                # Set default flags for better compatibility
                acroform[NameObject('/Flags')] = NumberObject(0)
                self.logger.debug("Added default Flags to AcroForm")

            # Ensure proper DR (Default Resources) if missing
            if '/DR' not in acroform:
                self.logger.debug("AcroForm missing default resources")
                # We'll let the PDF viewer handle this

            self.logger.info("AcroForm configured for optimal GhostScript compatibility")

        except Exception as e:
            self.logger.error("Error configuring AcroForm: {}".format(e))
            # Don't raise - continue with field processing
            
    def _process_fields_recursive(self, fields):
        """
        Process fields recursively to fix comb field issues

        Enhanced for pypdf 5.6.0 with better error handling and field counting.

        Returns:
            int: Number of fields successfully processed
        """
        fields_processed = 0

        if not fields:
            return fields_processed

        # Handle both direct arrays and indirect object references
        if isinstance(fields, IndirectObject):
            fields = fields.get_object()

        if not isinstance(fields, (list, ArrayObject)):
            self.logger.warning("Expected array of fields, got {}".format(type(fields)))
            return fields_processed

        for i, field_ref in enumerate(fields):
            try:
                # Safely resolve field reference
                if isinstance(field_ref, IndirectObject):
                    field = field_ref.get_object()
                else:
                    field = field_ref

                if not isinstance(field, DictionaryObject):
                    self.logger.warning("Field {} is not a dictionary object".format(i))
                    continue

                field_type = field.get('/FT', '')
                field_name = field.get('/T', 'Field_{}'.format(i))

                # Convert field name to string if it's a text object
                if hasattr(field_name, 'get_original_bytes'):
                    field_name = str(field_name)

                self.logger.debug("Processing field: {} (type: {})".format(field_name, field_type))

                # Process text fields (where comb fields are found)
                if field_type == '/Tx':
                    if self._fix_text_field(field, field_name):
                        fields_processed += 1
                elif field_type:
                    # Other field types - just count them
                    fields_processed += 1
                    self.logger.debug("Processed non-text field: {}".format(field_name))

                # Process child fields if they exist
                if '/Kids' in field:
                    child_count = self._process_fields_recursive(field['/Kids'])
                    fields_processed += child_count

            except Exception as e:
                self.logger.error("Error processing field {}: {}".format(i, e))
                # Continue processing other fields
                continue

        return fields_processed
                
    def _fix_text_field(self, field, field_name):
        """
        Fix text field for better GhostScript compatibility

        Enhanced for pypdf 5.6.0 with improved robustness and error handling.
        Special handling for numberOfCells fields for GhostScript 9.27 compatibility.

        Returns:
            bool: True if field was successfully processed
        """
        try:
            # Check if it's a comb field
            is_comb_field = False
            field_flags = 0

            if '/Ff' in field:
                try:
                    field_flags = int(field['/Ff'])
                    is_comb_field = bool(field_flags & (1 << 24))  # Comb flag is bit 24
                except (ValueError, TypeError) as e:
                    self.logger.warning("Invalid field flags in {}: {}".format(field_name, e))

            # Check for numberOfCells attribute (custom attribute for comb-like fields)
            numberOfCells = self._get_numberOfCells(field, field_name)
            if numberOfCells and numberOfCells > 0:
                is_comb_field = True  # Treat numberOfCells fields as comb fields
                self.logger.info("Field '{}' has numberOfCells: {}".format(field_name, numberOfCells))

            # Get current value with better handling
            value = field.get('/V', '')

            # Handle different value types
            if hasattr(value, 'get_original_bytes'):
                # TextStringObject
                value = str(value)
            elif isinstance(value, (bytes, bytearray)):
                try:
                    value = value.decode('utf-8', errors='replace')
                except Exception:
                    value = str(value)
            else:
                value = str(value) if value else ''

            # Skip empty fields unless they're comb fields (which might need setup)
            if not value and not is_comb_field:
                self.logger.debug("Skipping empty non-comb field: {}".format(field_name))
                return True

            self.logger.info("Processing field: {}, value: '{}', comb: {}, numberOfCells: {}".format(
                field_name, value, is_comb_field, numberOfCells))

            # Generate robust appearance stream with numberOfCells support
            self._generate_robust_appearance(field, field_name, value, is_comb_field, numberOfCells)

            # For comb fields, ensure proper DA string with explicit Tc
            if is_comb_field and '/DA' in field:
                self._optimize_comb_field_da(field, field_name, value, numberOfCells)

            # Special handling for numberOfCells fields for GhostScript 9.27
            if numberOfCells and numberOfCells > 0:
                self._apply_ghostscript_927_fixes(field, field_name, value, numberOfCells)

            return True

        except Exception as e:
            self.logger.error("Error fixing text field {}: {}".format(field_name, e))
            return False

    def _get_numberOfCells(self, field, field_name):
        """
        Extract numberOfCells value directly from PDF field attributes

        Searches for numberOfCells in multiple ways:
        1. Direct /numberOfCells attribute
        2. /MaxLen for comb fields (MaxLen often represents numberOfCells)
        3. Search all field keys for numberOfCells-related attributes
        4. For comb fields, use MaxLen as fallback

        Returns:
            int: Number of cells for this field, or None if not found
        """
        try:
            # 1. Direct search for numberOfCells attribute
            if '/numberOfCells' in field:
                try:
                    numberOfCells = int(field['/numberOfCells'])
                    if numberOfCells > 0:
                        self.logger.debug("Found direct numberOfCells={} for field '{}'".format(
                            numberOfCells, field_name))
                        return numberOfCells
                except (ValueError, TypeError):
                    self.logger.warning("Invalid numberOfCells value in field '{}'".format(field_name))

            # 2. Check if this is a comb field and use MaxLen
            is_comb_field = False
            if '/Ff' in field:
                try:
                    flags = int(field['/Ff'])
                    is_comb_field = bool(flags & (1 << 24))  # Comb flag is bit 24
                except (ValueError, TypeError):
                    pass

            if is_comb_field and '/MaxLen' in field:
                try:
                    max_len = int(field['/MaxLen'])
                    if max_len > 0:
                        self.logger.debug("Found numberOfCells={} from MaxLen for comb field '{}'".format(
                            max_len, field_name))
                        return max_len
                except (ValueError, TypeError):
                    pass

            # 3. Search all field keys for numberOfCells-related attributes
            for key in field.keys():
                key_str = str(key).lower()
                if ('numberofcells' in key_str or
                    'number_of_cells' in key_str or
                    'cellcount' in key_str or
                    'cells' in key_str):
                    try:
                        numberOfCells = int(field[key])
                        if numberOfCells > 0:
                            self.logger.debug("Found numberOfCells={} in key '{}' for field '{}'".format(
                                numberOfCells, key, field_name))
                            return numberOfCells
                    except (ValueError, TypeError):
                        continue

            # 4. For any field with MaxLen, try using it as numberOfCells
            if '/MaxLen' in field:
                try:
                    max_len = int(field['/MaxLen'])
                    if max_len > 0:
                        self.logger.debug("Using MaxLen={} as numberOfCells for field '{}'".format(
                            max_len, field_name))
                        return max_len
                except (ValueError, TypeError):
                    pass

            return None

        except Exception as e:
            self.logger.debug("Error getting numberOfCells for field {}: {}".format(field_name, e))
            return None

    def _optimize_comb_field_da(self, field, field_name, value, numberOfCells=None):
        """
        Optimize DA (Default Appearance) string for comb fields

        Enhanced for pypdf 5.6.0 with better error handling and validation.
        """
        try:
            # Get current DA string
            da_obj = field.get('/DA')
            if not da_obj:
                self.logger.warning("No DA string found for comb field {}".format(field_name))
                return

            current_da = str(da_obj)
            if not current_da.strip():
                self.logger.warning("Empty DA string for comb field {}".format(field_name))
                return

            # Extract font size from DA string with improved regex
            font_size = 12.0  # Default font size
            font_match = re.search(r'(\d+(?:\.\d+)?)\s+Tf', current_da)
            if font_match:
                try:
                    font_size = float(font_match.group(1))
                except ValueError:
                    self.logger.warning("Invalid font size in DA string for {}".format(field_name))
            else:
                self.logger.debug("No font size found in DA string for {}, using default".format(field_name))

            # Get field dimensions with validation
            if '/Rect' not in field:
                self.logger.warning("No Rect found for comb field {}".format(field_name))
                return

            rect = field['/Rect']
            if not isinstance(rect, (list, ArrayObject)) or len(rect) < 4:
                self.logger.warning("Invalid Rect for comb field {}".format(field_name))
                return

            try:
                field_width = float(rect[2]) - float(rect[0])
                # field_height = float(rect[3]) - float(rect[1])  # Not used currently
            except (ValueError, TypeError, IndexError):
                self.logger.warning("Invalid Rect values for comb field {}".format(field_name))
                return

            if field_width <= 0:
                self.logger.warning("Invalid field width for comb field {}".format(field_name))
                return

            # Get max length with validation, prefer numberOfCells if available
            if numberOfCells and numberOfCells > 0:
                max_len = numberOfCells
                self.logger.debug("Using numberOfCells={} for field '{}'".format(numberOfCells, field_name))
            else:
                max_len = field.get('/MaxLen', len(value) if value else 1)
                try:
                    max_len = int(max_len)
                    if max_len <= 0:
                        max_len = len(value) if value else 1
                except (ValueError, TypeError):
                    max_len = len(value) if value else 1

            # Calculate optimal character spacing
            cell_width = field_width / max_len
            char_width = font_size * 0.6  # Estimate character width (adjust based on font)
            tc_value = max(0.0, cell_width - char_width)

            # Update DA string with explicit Tc
            if re.search(r'\d+(?:\.\d+)?\s+Tc', current_da):
                # Replace existing Tc value
                new_da = re.sub(r'(\d+(?:\.\d+)?)\s+Tc', '{:.4f} Tc'.format(tc_value), current_da)
            else:
                # Add Tc to the end of DA string
                new_da = "{} {:.4f} Tc".format(current_da.rstrip(), tc_value)

            field[NameObject('/DA')] = TextStringObject(new_da)
            self.logger.info("Updated DA string for comb field '{}': Tc={:.4f}".format(field_name, tc_value))
            self.logger.debug("Full DA string: '{}'".format(new_da))

        except Exception as e:
            self.logger.error("Error optimizing DA for comb field {}: {}".format(field_name, e))
            # Don't raise - continue processing
            
    def _generate_robust_appearance(self, field, field_name, value, is_comb, numberOfCells=None):
        """
        Generate robust appearance stream for better GhostScript compatibility

        Enhanced for pypdf 5.6.0 with improved validation and error handling.
        Special handling for numberOfCells fields and GhostScript 9.27 compatibility.
        """
        try:
            # For GhostScript 9.27 compatibility, we need to generate explicit appearances
            # instead of just clearing them
            self._generate_gs927_compatible_appearance(field, field_name, value, is_comb, numberOfCells)

            # Ensure proper quadding (text alignment)
            if '/Q' not in field:
                # 0 = left, 1 = center, 2 = right
                alignment = 1 if is_comb else 0  # Center for comb fields, left for others
                field[NameObject('/Q')] = NumberObject(alignment)
                self.logger.debug("Set text alignment for field '{}': {}".format(field_name, alignment))

            # For comb fields, ensure MaxLen is properly set
            if is_comb:
                current_max_len = field.get('/MaxLen')
                # Prefer numberOfCells if available
                if numberOfCells and numberOfCells > 0:
                    new_max_len = numberOfCells
                    field[NameObject('/MaxLen')] = NumberObject(new_max_len)
                    self.logger.debug("Set MaxLen for numberOfCells field '{}': {}".format(field_name, new_max_len))
                elif not current_max_len or (value and len(value) > int(current_max_len)):
                    new_max_len = max(len(value), 1) if value else 1
                    field[NameObject('/MaxLen')] = NumberObject(new_max_len)
                    self.logger.debug("Set MaxLen for comb field '{}': {}".format(field_name, new_max_len))

            # Optimize field flags for better compatibility
            if '/Ff' in field:
                try:
                    flags = int(field['/Ff'])
                    original_flags = flags

                    # Remove ReadOnly flag (bit 0) if present to allow appearance generation
                    if flags & 1:
                        flags &= ~1
                        self.logger.debug("Removed ReadOnly flag from field '{}'".format(field_name))

                    # Ensure proper comb field flags
                    if is_comb:
                        # Ensure comb flag (bit 24) is set
                        flags |= (1 << 24)
                        # Ensure the field doesn't have multiline flag (bit 12)
                        flags &= ~(1 << 12)

                    if flags != original_flags:
                        field[NameObject('/Ff')] = NumberObject(flags)
                        self.logger.debug("Updated flags for field '{}': {} -> {}".format(field_name, original_flags, flags))

                except (ValueError, TypeError) as e:
                    self.logger.warning("Invalid field flags for {}: {}".format(field_name, e))

            # Ensure the field has a proper border for visibility
            if '/BS' not in field and '/Border' not in field:
                # Add a minimal border to ensure field visibility
                field[NameObject('/Border')] = ArrayObject([
                    NumberObject(0), NumberObject(0), NumberObject(1)  # [h_radius, v_radius, width]
                ])
                self.logger.debug("Added default border to field '{}'".format(field_name))

            self.logger.info("Prepared robust appearance for field '{}'".format(field_name))

        except Exception as e:
            self.logger.error("Error generating appearance for field {}: {}".format(field_name, e))
            # Don't raise - continue processing

    def _generate_gs927_compatible_appearance(self, field, field_name, value, is_comb, numberOfCells=None):
        """
        Generate GhostScript 9.27 compatible appearance

        This method creates explicit appearance dictionaries with normal (/N) appearance
        streams that GhostScript 9.27 can properly process, avoiding the
        "lacks the mandatory normal (N) appearance" error.
        """
        try:
            # Get field dimensions
            if '/Rect' not in field:
                self.logger.warning("No Rect found for field '{}'".format(field_name))
                return

            rect = field['/Rect']
            if not isinstance(rect, (list, ArrayObject)) or len(rect) < 4:
                self.logger.warning("Invalid Rect for field '{}'".format(field_name))
                return

            try:
                x1, y1, x2, y2 = float(rect[0]), float(rect[1]), float(rect[2]), float(rect[3])
                field_width = x2 - x1
                field_height = y2 - y1
            except (ValueError, TypeError, IndexError):
                self.logger.warning("Invalid Rect values for field '{}'".format(field_name))
                return

            if field_width <= 0 or field_height <= 0:
                self.logger.warning("Invalid field dimensions for '{}'".format(field_name))
                return

            # Create appearance dictionary with explicit normal appearance
            ap_dict = DictionaryObject()

            if value and value.strip():
                # Generate normal appearance stream for fields with values
                normal_appearance = self._create_normal_appearance_stream(
                    field, field_name, value, field_width, field_height, numberOfCells
                )
                if normal_appearance:
                    ap_dict[NameObject('/N')] = normal_appearance
                    self.logger.debug("Created normal appearance stream for field '{}'".format(field_name))
                else:
                    # Fallback: create minimal appearance dictionary
                    self._create_minimal_appearance(ap_dict, field_width, field_height)
                    self.logger.debug("Created minimal appearance for field '{}'".format(field_name))
            else:
                # For empty fields, create minimal appearance to satisfy GS 9.27
                self._create_minimal_appearance(ap_dict, field_width, field_height)
                self.logger.debug("Created empty appearance for field '{}'".format(field_name))

            # Set the appearance dictionary
            field[NameObject('/AP')] = ap_dict
            self.logger.info("Generated GS 9.27 compatible appearance for field '{}'".format(field_name))

        except Exception as e:
            self.logger.error("Error generating GS 9.27 appearance for field {}: {}".format(field_name, e))
            # Fallback: create minimal appearance to avoid GS errors
            try:
                ap_dict = DictionaryObject()
                self._create_minimal_appearance(ap_dict, 100, 20)  # Default dimensions
                field[NameObject('/AP')] = ap_dict
                self.logger.warning("Applied fallback appearance for field '{}'".format(field_name))
            except Exception as fallback_error:
                self.logger.error("Failed to apply fallback appearance: {}".format(fallback_error))

    def _create_minimal_appearance(self, ap_dict, field_width, field_height):
        """Create minimal appearance dictionary for GhostScript 9.27 compatibility"""
        ap_dict[NameObject('/N')] = DictionaryObject({
            NameObject('/Type'): NameObject('/XObject'),
            NameObject('/Subtype'): NameObject('/Form'),
            NameObject('/BBox'): ArrayObject([NumberObject(0), NumberObject(0),
                                            NumberObject(field_width), NumberObject(field_height)]),
            NameObject('/Length'): NumberObject(0)
        })

    def _apply_ghostscript_927_fixes(self, field, field_name, value, numberOfCells):
        """
        Apply specific fixes for GhostScript 9.27 compatibility with numberOfCells fields

        GhostScript 9.27 has stricter requirements for form field rendering:
        1. Requires explicit appearance streams for proper rendering
        2. More sensitive to character spacing calculations
        3. Needs specific font and positioning parameters
        """
        try:
            self.logger.info("Applying GhostScript 9.27 fixes for numberOfCells field '{}'".format(field_name))

            # Ensure the field is marked as a comb field
            if '/Ff' not in field:
                field[NameObject('/Ff')] = NumberObject(1 << 24)  # Set comb flag
            else:
                flags = int(field['/Ff'])
                flags |= (1 << 24)  # Ensure comb flag is set
                field[NameObject('/Ff')] = NumberObject(flags)

            # Force MaxLen to match numberOfCells exactly
            field[NameObject('/MaxLen')] = NumberObject(numberOfCells)

            # Ensure proper quadding for GhostScript 9.27
            field[NameObject('/Q')] = NumberObject(1)  # Center alignment works better in GS 9.27

            # Generate explicit appearance stream for GhostScript 9.27
            self._generate_gs927_appearance_stream(field, field_name, value, numberOfCells)

            # Set specific DA string optimized for GhostScript 9.27
            self._set_gs927_da_string(field, field_name, value, numberOfCells)

            self.logger.info("Applied GhostScript 9.27 fixes for field '{}'".format(field_name))

        except Exception as e:
            self.logger.error("Error applying GhostScript 9.27 fixes for field {}: {}".format(field_name, e))
            # Don't raise - continue processing

    def _generate_gs927_appearance_stream(self, field, field_name, value, numberOfCells):
        """
        Generate explicit appearance stream optimized for GhostScript 9.27

        GhostScript 9.27 requires explicit /AP dictionary with /N (normal) appearance.
        This method creates a proper appearance stream that GS 9.27 can process.
        """
        try:
            # Get field dimensions
            if '/Rect' not in field:
                self.logger.warning("No Rect found for field '{}'".format(field_name))
                return

            rect = field['/Rect']
            if not isinstance(rect, (list, ArrayObject)) or len(rect) < 4:
                self.logger.warning("Invalid Rect for field '{}'".format(field_name))
                return

            try:
                x1, y1, x2, y2 = float(rect[0]), float(rect[1]), float(rect[2]), float(rect[3])
                field_width = x2 - x1
                field_height = y2 - y1
            except (ValueError, TypeError, IndexError):
                self.logger.warning("Invalid Rect values for field '{}'".format(field_name))
                return

            if field_width <= 0 or field_height <= 0:
                self.logger.warning("Invalid field dimensions for '{}'".format(field_name))
                return

            # Create explicit appearance dictionary for GhostScript 9.27
            ap_dict = DictionaryObject()

            if value and value.strip():
                # Generate normal appearance stream for fields with values
                normal_appearance = self._create_normal_appearance_stream(
                    field, field_name, value, field_width, field_height, numberOfCells
                )
                if normal_appearance:
                    ap_dict[NameObject('/N')] = normal_appearance
                    self.logger.debug("Created normal appearance stream for field '{}'".format(field_name))
                else:
                    # Fallback: create minimal appearance dictionary
                    ap_dict[NameObject('/N')] = DictionaryObject({
                        NameObject('/Type'): NameObject('/XObject'),
                        NameObject('/Subtype'): NameObject('/Form'),
                        NameObject('/BBox'): ArrayObject([NumberObject(0), NumberObject(0),
                                                        NumberObject(field_width), NumberObject(field_height)]),
                        NameObject('/Length'): NumberObject(0)
                    })
                    self.logger.debug("Created minimal appearance for field '{}'".format(field_name))
            else:
                # For empty fields, create minimal appearance to satisfy GS 9.27
                ap_dict[NameObject('/N')] = DictionaryObject({
                    NameObject('/Type'): NameObject('/XObject'),
                    NameObject('/Subtype'): NameObject('/Form'),
                    NameObject('/BBox'): ArrayObject([NumberObject(0), NumberObject(0),
                                                    NumberObject(field_width), NumberObject(field_height)]),
                    NameObject('/Length'): NumberObject(0)
                })
                self.logger.debug("Created empty appearance for field '{}'".format(field_name))

            # Set the appearance dictionary
            field[NameObject('/AP')] = ap_dict
            self.logger.info("Generated GS 9.27 compatible appearance for field '{}'".format(field_name))

        except Exception as e:
            self.logger.error("Error generating GS 9.27 appearance for field {}: {}".format(field_name, e))
            # Fallback: create minimal appearance to avoid GS errors
            try:
                field[NameObject('/AP')] = DictionaryObject({
                    NameObject('/N'): DictionaryObject({
                        NameObject('/Type'): NameObject('/XObject'),
                        NameObject('/Subtype'): NameObject('/Form'),
                        NameObject('/BBox'): ArrayObject([NumberObject(0), NumberObject(0),
                                                        NumberObject(100), NumberObject(20)]),
                        NameObject('/Length'): NumberObject(0)
                    })
                })
                self.logger.warning("Applied fallback appearance for field '{}'".format(field_name))
            except Exception as fallback_error:
                self.logger.error("Failed to apply fallback appearance: {}".format(fallback_error))

    def _create_normal_appearance_stream(self, field, field_name, value, field_width, field_height, numberOfCells):
        """
        Create a normal appearance stream for GhostScript 9.27 compatibility

        This generates a proper PDF form XObject with the field's content rendered
        according to its DA (Default Appearance) string and comb field properties.
        """
        try:
            from pypdf.generic import StreamObject

            # Get DA string for font and formatting information
            da_string = str(field.get('/DA', '/Helvetica 12 Tf 0 g'))

            # Extract font size from DA string
            font_size = 12.0
            font_match = re.search(r'(\d+(?:\.\d+)?)\s+Tf', da_string)
            if font_match:
                try:
                    font_size = float(font_match.group(1))
                except ValueError:
                    pass

            # Calculate text positioning
            text_y = (field_height - font_size) / 2.0  # Center vertically
            text_x = 2.0  # Small left margin

            # For comb fields, calculate character spacing
            if numberOfCells and numberOfCells > 0:
                cell_width = field_width / numberOfCells
                char_spacing = max(0.0, cell_width - font_size * 0.6)
                # Adjust starting position for comb fields
                text_x = cell_width / 2.0 - font_size * 0.3
            else:
                char_spacing = 0.0

            # Create appearance stream content
            # This is a simplified PDF content stream that renders the text
            stream_content = []
            stream_content.append("q")  # Save graphics state
            stream_content.append("BT")  # Begin text

            # Apply DA string (font, size, color)
            if da_string.strip():
                stream_content.append(da_string)

            # Set character spacing if needed
            if char_spacing > 0:
                stream_content.append("{:.2f} Tc".format(char_spacing))

            # Position and show text
            stream_content.append("{:.2f} {:.2f} Td".format(text_x, text_y))

            # Escape text for PDF
            escaped_text = value.replace('\\', '\\\\').replace('(', '\\(').replace(')', '\\)')
            stream_content.append("({}) Tj".format(escaped_text))

            stream_content.append("ET")  # End text
            stream_content.append("Q")   # Restore graphics state

            # Join content with newlines
            content_string = "\n".join(stream_content)
            content_bytes = content_string.encode('latin-1')

            # Create the appearance stream object
            appearance_stream = StreamObject.create_decoded_stream_object(
                DictionaryObject({
                    NameObject('/Type'): NameObject('/XObject'),
                    NameObject('/Subtype'): NameObject('/Form'),
                    NameObject('/BBox'): ArrayObject([
                        NumberObject(0), NumberObject(0),
                        NumberObject(field_width), NumberObject(field_height)
                    ]),
                    NameObject('/Length'): NumberObject(len(content_bytes))
                }),
                content_bytes
            )

            self.logger.debug("Created appearance stream for field '{}' with content: {}".format(
                field_name, content_string.replace('\n', ' ')))

            return appearance_stream

        except Exception as e:
            self.logger.error("Error creating appearance stream for field {}: {}".format(field_name, e))
            return None

    def _set_gs927_da_string(self, field, field_name, value, numberOfCells):
        """
        Set DA string optimized for GhostScript 9.27 with numberOfCells
        """
        try:
            # Get current DA string or create a default one
            current_da = str(field.get('/DA', '/Helvetica 12 Tf 0 g'))

            # Extract font size
            font_size = 12.0
            font_match = re.search(r'(\d+(?:\.\d+)?)\s+Tf', current_da)
            if font_match:
                try:
                    font_size = float(font_match.group(1))
                except ValueError:
                    pass

            # Get field width for spacing calculation
            if '/Rect' in field:
                rect = field['/Rect']
                try:
                    field_width = float(rect[2]) - float(rect[0])
                except (ValueError, TypeError, IndexError):
                    field_width = 100.0  # Default fallback
            else:
                field_width = 100.0

            # Calculate character spacing for GhostScript 9.27
            # GS 9.27 is more sensitive to Tc values
            cell_width = field_width / numberOfCells
            char_width = font_size * 0.5  # More conservative estimate for GS 9.27
            tc_value = max(0.0, cell_width - char_width)

            # For GhostScript 9.27, use more conservative Tc values
            tc_value = min(tc_value, font_size * 2.0)  # Cap the spacing

            # Create optimized DA string for GhostScript 9.27
            if re.search(r'\d+(?:\.\d+)?\s+Tc', current_da):
                new_da = re.sub(r'(\d+(?:\.\d+)?)\s+Tc', '{:.2f} Tc'.format(tc_value), current_da)
            else:
                new_da = "{} {:.2f} Tc".format(current_da.rstrip(), tc_value)

            field[NameObject('/DA')] = TextStringObject(new_da)

            self.logger.info("Set GS 9.27 optimized DA for field '{}': Tc={:.2f}".format(field_name, tc_value))
            self.logger.debug("GS 9.27 DA string: '{}'".format(new_da))

        except Exception as e:
            self.logger.error("Error setting GS 9.27 DA for field {}: {}".format(field_name, e))

    def save(self, output_path):
        """
        Save the fixed PDF with enhanced error handling

        Enhanced for pypdf 5.6.0 with better compression and error reporting.
        """
        if not self.writer:
            self.logger.error("No writer available for saving")
            return False

        output_path = Path(output_path)

        try:
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Save with compression enabled
            with open(output_path, 'wb') as output_file:
                self.writer.write(output_file)

            # Verify the file was written
            if output_path.exists() and output_path.stat().st_size > 0:
                self.logger.info("Successfully saved fixed PDF to: {}".format(output_path))
                self.logger.info("Output file size: {:,} bytes".format(output_path.stat().st_size))
                return True
            else:
                self.logger.error("Output file was not created or is empty")
                return False

        except PermissionError as e:
            self.logger.error("Permission denied writing to {}: {}".format(output_path, e))
            return False
        except OSError as e:
            self.logger.error("OS error saving PDF to {}: {}".format(output_path, e))
            return False
        except Exception as e:
            self.logger.error("Unexpected error saving PDF: {}".format(e))
            return False

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.close()
        # Return None to propagate any exceptions

    def close(self):
        """Clean up resources"""
        if hasattr(self, 'reader') and self.reader:
            # PyPDF 5.0.0 supports context managers
            if hasattr(self.reader, 'stream') and hasattr(self.reader.stream, 'close'):
                try:
                    self.reader.stream.close()
                except Exception as e:
                    self.logger.debug("Error closing reader stream: {}".format(e))

        self.reader = None
        self.writer = None
        self.logger.debug("Resources cleaned up")

def main():
    """
    Main function to process PDF files

    Requires pypdf 5.6.0 for optimal performance and compatibility.
    """
    import argparse

    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="PDF Comb Field GhostScript Compatibility Fixer (pypdf 5.6.0) - Enhanced for numberOfCells fields",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python comb_compatibility.py input.pdf output.pdf
  python comb_compatibility.py --verbose input.pdf output.pdf
  python comb_compatibility.py --gs927 input.pdf output.pdf
  python comb_compatibility.py --quiet input.pdf output.pdf

This tool fixes comb field issues in PDF forms to improve compatibility
with GhostScript flattening operations, especially GhostScript 9.27.

Enhanced features:
- Special handling for numberOfCells attributes
- GhostScript 9.27 specific compatibility fixes (use --gs927 flag)
- Direct detection of numberOfCells from PDF field attributes
- Optimized character spacing for older GhostScript versions
- Explicit appearance stream generation for GS 9.27 compatibility
        """
    )

    parser.add_argument('input_pdf', help='Input PDF file path')
    parser.add_argument('output_pdf', help='Output PDF file path')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Suppress all output except errors')
    parser.add_argument('--gs927', action='store_true',
                       help='Enable GhostScript 9.27 compatibility mode (generates explicit appearance streams)')

    parser.add_argument('--version', action='version',
                       version='PyPDF Comb Field Fixer (pypdf 5.6.0) - Enhanced for numberOfCells & GhostScript 9.27')

    args = parser.parse_args()

    # Configure logging based on arguments
    if args.quiet:
        logging.getLogger().setLevel(logging.ERROR)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)

    logger = logging.getLogger(__name__)

    logger.info("PDF Comb Field GhostScript Compatibility Fixer")
    logger.info("Enhanced for numberOfCells fields and GhostScript 9.27 compatibility")
    logger.info("pypdf Version: {}".format(pypdf.__version__))
    logger.info("Input PDF: {}".format(args.input_pdf))
    logger.info("Output PDF: {}".format(args.output_pdf))
    if args.gs927:
        logger.info("GhostScript 9.27 compatibility mode: ENABLED")
    logger.info("-" * 70)

    try:
        # Use context manager for automatic cleanup
        with CombFieldFixer(args.input_pdf, gs927_mode=args.gs927) as fixer:
            if fixer.fix_comb_fields():
                if fixer.save(args.output_pdf):
                    logger.info("✓ PDF successfully fixed for GhostScript compatibility")
                    logger.info("Now you can flatten the PDF with:")
                    logger.info("  gs -o flattened.pdf -sDEVICE=pdfwrite {}".format(args.output_pdf))
                    return 0
                else:
                    logger.error("✗ Failed to save the fixed PDF")
                    return 1
            else:
                logger.error("✗ Failed to fix PDF comb fields")
                return 1

    except FileNotFoundError as e:
        logger.error("✗ File not found: {}".format(e))
        return 1
    except PdfReadError as e:
        logger.error("✗ PDF read error: {}".format(e))
        return 1
    except Exception as e:
        logger.error("✗ Unexpected error: {}".format(e))
        if args.verbose:
            import traceback
            logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())